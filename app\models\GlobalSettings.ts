import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Delete } from "../apiServices/JC_Delete";

export class GlobalSettingsModel {

    // - -------- - //
    // - SERVICES - //
    // - -------- - //

    static apiRoute:string = "globalSettings";
    static async Get(code: string) {
        return await JC_Get<GlobalSettingsModel>(this.apiRoute, { code }, GlobalSettingsModel);
    }
    static async GetList() {
        return await JC_GetList<GlobalSettingsModel>(`${this.apiRoute}/getList`, GlobalSettingsModel, undefined, {});
    }
    static async Create(data: GlobalSettingsModel) {
        return await JC_Put<GlobalSettingsModel>(this.apiRoute, data);
    }
    static async CreateList(dataList: GlobalSettingsModel[]) {
        return await JC_Put<GlobalSettingsModel[]>(`${this.apiRoute}/createList`, dataList);
    }
    static async Update(data: GlobalSettingsModel) {
        return await JC_Post<GlobalSettingsModel>(this.apiRoute, data);
    }
    static async UpdateList(dataList: GlobalSettingsModel[]) {
        return await JC_Post<GlobalSettingsModel[]>(`${this.apiRoute}/updateList`, dataList);
    }
    static async Delete(code: string) {
        return await JC_Delete(this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_Post(`${this.apiRoute}/deleteList`, { codes });
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Description: string;
    Value: string;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<GlobalSettingsModel>) {
        this.Code = "";
        this.Description = "";
        this.Value = "";
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Code} | ${this.Description} | ${this.Value}`;
    }
}
